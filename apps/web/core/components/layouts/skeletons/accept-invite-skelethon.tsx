export function AcceptInviteSkelethon() {
	return (
		<div className="w-full h-full flex items-center justify-center">
			<div className="w-full max-w-2xl mx-auto p-6  bg-white animate-pulse space-y-6">
				<div className="space-y-2">
					<div className="h-6 bg-gray-200 rounded w-1/3"></div>
					<div className="h-4 bg-gray-100 rounded w-2/3"></div>
				</div>

				<div className="space-y-4">
					<div className="h-10 bg-gray-100 rounded"></div>
					<div className="h-10 bg-gray-100 rounded"></div>
					<div className="h-10 bg-gray-100 rounded w-2/3 mx-auto"></div>
				</div>

				<div className="pt-4">
					<div className="h-12 bg-gray-200 rounded w-full"></div>
				</div>
			</div>
		</div>
	);
}
