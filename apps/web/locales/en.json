{"TITLE": "Ever Teams", "sidebar": {"DASHBOARD": "Dashboard", "FORYOU": "General", "HOME": "Home", "INBOX": "Inbox", "PROJECTS": "Projects", "FAVORITES": "Favorites", "TASKS": "Tasks", "MY_TASKS": "My Tasks", "TEAMTASKS": "Team's Tasks", "MY_WORKS": "My Work", "TIME_AND_ACTIVITY": "Time & Activity", "WORK_DIARY": "Work Diary", "REPORTS": "Reports", "TIMESHEETS": "Timesheets", "MANUAL_TIME_EDIT": "Manual Time Edit", "WEEKLY_LIMIT": "Weekly Limit", "ACTUAL_AND_EXPECTED_HOURS": "Actual & Expected Hours", "PAYMENTS_DUE": "Payments Due", "PROJECT_BUDGET": "Project Budget"}, "common": {"APPLICATION": "Application", "TIME_SPENT": "Time Spent", "PERCENT_USED": "Percent Used", "NO_ACTIVITY_DATA": "No activity data available", "SELECT_DIFFERENT_DATE": "Select a different date range or check back later", "PENDING_TASKS": "Pending Tasks", "SHOW_MORE": "Show More", "TASKS": "Tasks", "MEN_HOURS": "Men Hours", "MEMBERS_WORKED": "Members Worked", "VIEW": "View", "VIEW_INFO": "View info", "SOURCE": "Source", "CLIENT": "Client", "NO_CLIENT": "No client", "SCREENSHOTS": "Screenshots", "TIME_LOG": "Time log", "TO_DO": "To-do", "PRODUCTIVE": "Productive", "NEUTRAL": "Neutral", "UNPRODUCTIVE": "Unproductive", "ACTIVITIES": "activities", "MEMBERS": "Members", "NO_PROJECT": "No Project", "DURATION": "Duration", "PROGRESS": "Progress", "EMPLOYEE": "Employee", "NO_MEMBER_DATA": "No Member Data", "NO_PROJECT_DATA": "No Project Data", "NO_MEMBER": "No Member", "DAILY": "Daily", "WEEKLY": "Weekly", "ITEMS_SELECTED": "{count} item(s) selected", "SELECT_ITEMS": "Select items", "COLLAPSE_COLUMN": "Collapse Column", "EXPAND_COLUMN": "Expand Column", "EDIT_STATUS": "Edit Status", "ADD_TIME_ENTRY": "Add Time Entry", "OPT_IN_UPDATES": "Opt-in to receive updates and news about Ever Teams.", "SUBSCRIBE_NEWSLETTER": "Subscribe to our newsletter", "VIEW_PROJECT": "View Project", "SHARE_PROJECT": "Share Project", "DELETE_PROJECT": "Delete Project", "DELETE_CONFIRMATION": "Are you sure you want to delete this?", "IRREVERSIBLE_ACTION_WARNING": "This action is irreversible. All related data will be lost.", "SHIFT_TIMING": "Shift Timing", "ADD_ANOTHER_PERIOD": "Add Another Period", "REMOVE_PERIOD": "Remove Period", "DATE_AND_TIME": "Date and Time", "NOTES": "Notes", "CHANGE_STATUS": "Change Status", "TYPES": "Types", "LINK_TO_PROJECT": "Link to Project", "SELECT_A_PROJECT": "Select a Project", "ACCEPT": "Accept", "ACCEPTED": "Accepted", "REJECT": "Reject", "MEMBER": "Member", "ACTION": "Action", "TOMORROW": "Tomorrow", "YESTERDAY": "Yesterday", "REJECTED": "Rejected", "VERIFY": "Verify", "CARDS": "Cards", "TABLE": "Table", "KANBAN": "Ka<PERSON><PERSON>", "TEAMS": "Teams", "COMBINE": "Combine", "INVITE": "Invite", "INVITED": "Invited", "EXPIRE": "Expire", "EXPIRED": "Expired", "REQUEST": "Request", "REQUESTED": "Requested", "LABEL": "Label", "LABELS": "Labels", "POSITION": "Position", "ROLES": "Roles", "JOIN_OR_LEFT": "Joined / Left", "PLEASE": "Please", "HERE": "here", "PEOPLE": "People", "COLLABORATE": "Collaborate", "NO": "No", "ONLY_ME": "Only me", "YES": "Yes", "BASIC": "Basic", "DELETE": "Delete", "MEET": "Meet", "BOARD": "Board", "SECURITY_CODE": "Security Code", "CONFIRM": "Confirm", "COST_BASED": "Cost-Based", "HOURS_BASED": "Hours-Based", "MANAGER": "Manager", "BUDGET_TYPE": "Budget Type", "CLEAR_FILTERS": "Clear Filters", "APPLY_FILTERS": "Apply Filters", "YOUR_EMAIL": "your email", "CONTINUE": "Continue", "EDIT_TASK": "Edit Task", "ASSIGN_TASK": "Assign Task", "ASSIGN_TASK_TO": "Assign Task", "REMOVE_ACCOUNT": "Remove Account", "REMOVE_EVERYWHERE": "<PERSON><PERSON><PERSON> From All Teams", "DELETE_ACCOUNT": "Delete This Account", "DELETE_ALL_DATA": "Delete All Data", "UNASSIGN_TASK": "Unassign Task", "MAKE_A_MANAGER": "Make a Manager", "UNMAKE_A_MANAGER": "Unmake a Manager", "TRANSFERT_OWNERSHIP": "Transfer Ownership", "TRANSFERT_OWNERSHIP_TO": "Transfer full ownership of the team to another user", "REMOVE": "Remove", "REMOVE_TEAM": "Remove Team", "DISPOSE_TEAM": "Dispose Team", "QUIT_TEAM": "Quit the Team", "QUIT": "Quit", "STATUSES": "Statuses", "RESEND_INVITATION": "Resend Invitation", "TODAY": "Today", "TOTAL": "Total", "ESTIMATED": "Estimated", "ESTIMATE": "Estimate", "STATUS": "Status", "NAME": "Name", "TASK": "Task", "MY_TASKS": "My Tasks", "WORKED_ON_TASK": "Worked on Task", "TOTAL_WORKED_TODAY": "Total worked Today", "TOTAL_WORK": "Today work", "OPEN": "Open", "NOW": "Now", "NEXT": "Next", "PREV": "Previous", "DETAILS": "Details", "CLOSED": "Closed", "CLOSE": "Close", "USER_NOT_FOUND": "No users found.", "TASK_TITTLE": "Task Title", "PUBLIC_TASK": "This task is Public", "PRIVATE_TASK": "This task is Private", "PUBLIC_TASK_LABEL": "Make a public", "PRIVATE_TASK_LABEL": "Make a private", "CREATE_TASK": "Create New Task", "REMOVE_FAVORITE_TASK": "Remove Task from Favorites", "ADD_FAVORITE_TASK": "Add Task to Favorites", "NO_FAVORITE_TASK": "No Favorite Tasks Yet", "LINK_TASK": "Select Related Issue", "LINK": "Link", "LINKS": "Links", "SHOW": "Show", "ADD_LINK": "Add Links", "COMMENT": "Comment", "DESCRIPTION": "Description", "CHILD_ISSUE_TASK": "Select Child Issue", "CREATE_TEAM": "Create New Team", "CREATE_PROJECT": "Create New Project", "ALL_TEAMS": "All Teams", "VERIFY_ACCOUNT_MSG": "Please Verify your account before start using the app", "CREATE_ROLE": "Create Role", "CREATE": "Create", "CREATE_ISSUE": "Create issue", "NEW_ISSUE": "New Issue", "ACTIVATED": "Activated", "DEACTIVATED": "Deactivated", "PERIOD": "Period", "NEW": "New", "CREATE_VERSION": "Create Version", "VERSION": "Version", "LOADING": "Loading", "TOTAL_TIME": "Total time", "LAST_24_HOURS": "Last 24 Hours", "WORKED": "Worked", "ASSIGNED": "Assigned", "SELECT_STATUS": "Select status", "SELECT_ISSUE": "Select Issue", "UNASSIGNED": "Unassigned", "TASK_DETAILS": "Task Details", "TYPE_SOMETHING": "Type something", "FILTER": "Filter", "APPLY": "Apply", "RESET": "Reset", "LOGOUT": "Log Out", "THEMES": "Themes", "3D_MODE": "3D Mode", "DARK_MODE": "Dark Mode", "SETTINGS": "Settings", "PERSONAL": "Personal", "USE_SETTING": "Use setting", "TEAM": "Team", "MY_TEAM": "My Team", "FULL_NAME": "Full Name", "USER_AVATAR": "User Avatar", "CONTACT": "Contact", "ENABLED": "Enabled", "DISABLED": "Disabled", "THEME": "Theme", "TIME_ZONE": "Timezone", "EDIT": "Edit", "SAVE": "Save", "LANGUAGE": "Language", "DETECT": "Detect", "TRANSFER": "Transfer", "TRANSFER_TEAM": "Transfer Team", "TEAM_MEMBERS": "Team Members", "NO_TEAM": "Create your team or join existing", "NO_TEAM_SUB": "It's great to work with others so create a team and invite everyone to collaborate!", "NO_TEAM_TOOLTIP": "You need to verify your email address", "CANCEL": "Cancel", "DISCARD": "Discard", "EXISTING_MEMBER": "Already a Member", "NEW_MEMBER": "Become a Member", "TEAM_MEMBER_EMAIL_ADDRESS": "Team member email address", "BACK": "Back", "DONT_HAVE_ACCOUNT": "Don't have an account?", "REGISTER": "Register Now!", "JOIN_REQUEST": "Request To Join", "PERMISSION": "Permission", "SEARCH": "Search", "ADD_PARENT": "Add Parent", "PARENT_TASK_ASSIGNED_SUCCESS": "Parent task \"{parentTitle}\" has been successfully assigned to \"{childTitle}\"", "PARENT_TASK_ASSIGNMENT_FAILED": "Failed to assign task \"{childTitle}\" to parent \"{parentTitle}\"", "PARENT_TASK_ASSIGNMENT_ERROR": "An error occurred while assigning parent task \"{parentTitle}\" to task \"{childTitle}\". Please try again.", "BLOCKS": "Blocks", "CLONES": "<PERSON><PERSON><PERSON>", "DUPLICATES": "Duplicates", "IS_BLOCKED_BY": "Is Blocked By", "IS_CLONED_BY": "Is Cloned By", "IS_DUPLICATED_BY": "Is Duplicated By", "RELATES_TO": "Relates To", "CHANGE_PARENT": "Change Parent", "RELATED_ISSUES": "Related Issues", "CHILD_ISSUES": "Child Issues", "REOPEN": "Reopen", "NO_TASKS": "No Tasks", "FULL_WIDTH": "Full width", "TASK_INPUT_DISABLED_MESSAGE_WHEN_TIMER_RUNNING": "Please stop the Timer before changing the Task", "COLLABORATE_DIALOG_TITLE": "Start Collaboration", "COLLABORATE_DIALOG_SUB_TITLE": "Invite member(s) and start collaborating", "COLLABORATE_DIALOG_FOOTER_MESSAGE": "Start Meeting or Board Collaboration", "ISSUE_TYPE": "Issue Type", "ACTIVITY": "Activity", "FILTER_ALL": "All", "FILTER_COMMENTS": "Comments", "FILTER_HISTORY": "History", "FILTER_UNSUBSCRIBE": "Unsubscribe", "KEYBOARD_SHORTCUTS": "Keyboard Shortcuts", "GITHUB_LOADING_TEXT": "We are now installing your GitHub Integration, hold on...", "GITHUB_INTEGRATION_SUBTITLE_TEXT": "Activate GitHub Integration for project & repository sync", "GITHUB_INTEGRATION_AUTO_SYNC_TASK_TEXT": "Automatically synchronize tasks in your application for seamless and efficient updates.", "GITHUB_INTEGRATION_LABEL_SYNC_TASK_TEXT": "Synchronize tasks selectively by associating them with specific labels.", "GITHUB_AUTO_SYNC_LABEL": "Select Auto-Sync Label", "THERE_IS_NO_TASK_ASSIGNED": "There is no task assigned", "NO_USERS_ONLINE": "There are no users online", "NOT_FOUND": "Not Found", "PAGE_NOT_FOUND": "Page not found", "NO_USERS_WORKING": "There are currently no active users", "NO_USERS_PAUSED_WORK": "There are currently no users who have paused their work", "NO_USERS_IDLE": "There are currently no inactive users", "SELECT_TEAM_MEMBER": "Select team member", "ALL_MEMBERS": "All Members", "NOT_WORKING": "Not Working", "WORKING": "Working", "PAUSED": "Paused", "ONLINE": "Online", "SKIP_ADD_LATER": "Add later", "DAILYPLAN": "Planned", "INVITATION_SENT": "Invitation Sent Successfully", "INVITATION_SENT_TO_USER": "A team invitation has been sent to {email}.", "CALENDAR": "Calendar", "SELECT": "Select", "SAVE_CHANGES": "Save changes", "plan": {"SINGULAR": "Plan", "PLURAL": "Plans", "CHOOSE_DATE": "Select a date to be able to see a plan", "PLAN_NOT_FOUND": "Plan not found", "FOR_DATE": "PLAN FOR {date}", "FOR_TODAY": "Today's plan", "FOR_TOMORROW": "Tomorrow's plan", "EDIT_PLAN": "Edit Plan", "TRACKED_TIME": "Tracked time", "SEE_PLANS": "See plans", "ADD_PLAN": "Add Plan", "DELETE_THIS_PLAN": "Delete this plan"}, "DAYOFWEEK": {"Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "Sunday": "Sunday"}, "teamStats": {"MEMBER": "Member", "MEMBERS_WORKED": "Members Worked", "TOTAL_TIME": "Total Time", "TOTAL_HOURS": "Total Hours", "TRACKED": "Tracked", "MANUALLY_ADDED": "Manually Added", "MANUAL": "Manual", "ACTIVE_TIME": "Active Time", "IDLE_TIME": "Idle Time", "IDLE": "Idle", "UNKNOWN_ACTIVITY": "Unknown Activity", "ACTIVITY_LEVEL": "Activity Level", "NO_DATA_AVAILABLE": "No Data Available"}, "timesheets": {"SINGULAR": "Timesheet", "PLURAL": "Timesheets"}, "DAYS": {"sun": "Sun", "mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat"}, "COPY_ISSUE_LINK": "Copy issue link", "MAKE_A_COPY": "Make a copy", "ASSIGNEE": "Assignee", "CHANGE_RELATIONS": "Change relations", "SET_AS_NEXT": "Set as next", "MOVE_TO": "Move to", "SELECT_DATE": "Select date", "SELECT_AND_CLOSE": "Select & Close", "SORT": "Sort", "ICON": "Icon", "SELECT_ROLE": "Please Select any Role", "ADD_TIME": "Add Time", "VIEW_TIMESHEET": "View timesheet", "FILTER_TODAY": "Today", "FILTER_LAST_7_DAYS": "Last 7 Days", "FILTER_LAST_30_DAYS": "Last 30 Days", "FILTER_THIS_YEAR": "This year {year}", "FILTER_CUSTOM_RANGE": "Custom Range", "CLEAR_FILTER": "Clear Filter", "CLEAR": "Clear", "APPLY_FILTER": "Apply Filter", "CREATE_NEW": "Create New", "NO_RESULT": "No result", "PICK_A_DATE": "Pick a date", "THIS_WEEK": "This week", "LAST_WEEK": "Last week", "THIS_MONTH": "This month", "LAST_MONTH": "Last month", "LAST_TWO_WEEKS": "Last two weeks", "DATE": "Date", "WEEK": "Week", "GROUP_BY": "Group by", "DAILY_LIMIT": "Daily limit", "WEEKLY_LIMIT": "Weekly limit", "REPORTS": "Reports", "LEGEND": "Legend", "HOURS": "Hours", "START_DATE": "Start Date", "END_DATE": "End Date", "MANAGERS": "Managers", "NOT_HOURS_WORKED": "Not hours worked", "MEMBERS_COUNT": "Members count", "REVIEW": "REVIEW", "ARCHIVE": "Archive", "ARCHIVE_AT": "Archive at", "RESTORE": "Rest<PERSON>", "HIDE": "<PERSON>de", "EXPORT": "Export", "PDF": "PDF", "XLSX": "XLSX"}, "dashboard": {"APPS_URLS_DASHBOARD": "Apps & URLs Dashboard", "TEAM_DASHBOARD": "Team Dashboard", "PRODUCTIVITY_BREAKDOWN": "Productivity breakdown for {month}", "DAILY": "Daily", "WEEKLY": "Weekly", "MONTHLY": "Monthly"}, "timeActivity": {"TIME_AND_ACTIVITY": "Time and Activity", "TOTAL_HOURS": "Total Hours", "AVERAGE_ACTIVITY": "Average Activity", "TOTAL_EARNINGS": "Total Earnings", "TRACKED_HOURS": "Tracked Hours", "EARNINGS": "Earnings", "ACTIVITY_LEVEL": "Activity Level", "NO_ACTIVITY_DATA": "No Activity Data", "NO_ACTIVITY_DATA_MESSAGE": "There is no activity data to display for the selected time period.", "START_TRACKING_MESSAGE": "Start tracking your time to see the data here.", "NO_ACTIVITY": "No activity", "UNNAMED_EMPLOYEE": "Unnamed Employee", "NO_TIME_ACTIVITY": "No time activity", "HOURS_LABEL": "Hours:", "EARNINGS_LABEL": "Earnings:", "AVERAGE_ACTIVITY_LABEL": "Average Activity:", "MEMBER_HEADER": "Member ↑", "PROJECT_HEADER": "Project ↑", "TRACKED_HOURS_HEADER": "Tracked Hours ↑", "EARNINGS_HEADER": "Earnings ↑", "ACTIVITY_LEVEL_HEADER": "Activity Level ↑", "SHOW_ENTRIES": "Show {count}", "SHOWING_ENTRIES": "Showing {start} to {end} of {total} entries", "FIRST": "First", "PREVIOUS": "Previous", "NEXT": "Next", "LAST": "Last", "GROUP_BY": "Group by", "DATE": "Date", "VIEW": "View", "HOURS_ON_DATE": "{hours} hours on {date}"}, "hotkeys": {"HELP": "Help", "TO_OPEN_SHORTCUT_LIST": "To open the shortcut list", "TIMER": "Timer", "START_TIMER": "Start Timer", "STOP_TIMER": "Stop Timer", "TASK": "Task", "ASSIGN_TASK": "Assign Task", "CREATE_TASK": "Create Task"}, "alerts": {"REAL_TIME_ON_WORKING": "We are working on Real-Time Sync at the moment, please check on this feature later.", "ALERT_DELETE_ACCOUNT": "You will be removed from all teams, except where you are the only manager in the team", "ALERT_ACCOUNT_PERMANENT_DELETE": "Your Account will be deleted permanently with removing from all teams", "ALERT_REMOVE_ALL_DATA": "All Account Data will be removed from all teams where you are ONLY one existing manager", "ALERT_REMOVE_TEAM": "Team will be completely removed from the system and team members will lose access", "ALERT_QUIT_TEAM": "You are about to quit the team", "ALERT_USER_DELETED_FROM_TEAM": "You have been deleted from the team"}, "pages": {"timeLimitReport": {"TIME_SPENT": "Time spent", "LIMIT": "Limit", "PERCENTAGE_USED": "Percentage used", "REMAINING": "Remaining", "GROUP_BY": "Group by", "DAILY_LIMIT": "Daily limit", "WEEKLY_LIMIT": "Weekly limit"}, "projects": {"projectTitle": {"PLURAL": "Projects", "SINGULAR": "Project"}, "CREATE_NEW_PROJECT": "Create New Project", "views": {"LIST_VIEW": "List View", "GRID_VIEW": "Grid View"}, "basicInformationForm": {"formFields": {"title": "Project Title", "titlePlaceholder": "Client Project #1...", "description": "Description", "datePickerPlaceholder": "Pick a date", "websiteUrl": "Website URL", "websiteUrlPlaceholder": "https://example.com", "projectThumbnail": "Project Thumbnail", "uploadPhoto": "Upload photo"}, "errors": {"titleRequired": "Project title is required.", "titleLength": "Project title must be between 3 and 100 characters.", "invalidWebsiteUrl": "Invalid website URL.", "fileSizeLimit": "File size must be less than 5MB.", "fileFormatLimit": "Only JPG and PNG formats are allowed.", "startDateRequired": "Start date is required", "endDateRequired": "End date is required", "endDateAfterStart": "End date must be after start date", "invalidUrl": "Please enter a valid URL", "uploadError": "Something went wrong while uploading the project image, please try again!"}, "common": {"search": "Search ...", "typeNew": "Type new ...", "addNew": "Add new", "uploadingImage": "Uploading the project image"}}, "categorizationForm": {"formFields": {"labels": "Labels", "labelsPlaceholder": "Select labels...", "tags": "Tags", "tagsPlaceholder": "Select tags...", "colorCode": "Color Code"}}, "financialSettingsForm": {"formFields": {"budgetType": "Budget Type", "budgetTypePlaceholder": "Select a budget type", "budgetAmount": "Budget Amount", "budgetAmountPlaceholder": "10 000$...", "currency": "Currency Selection", "currencyPlaceholder": "Select a currency...", "billing": "Billing Type", "billingPlaceholder": "Select interval..."}}, "teamAndRelationsForm": {"formFields": {"assignMembers": "Assign Members & Managers", "noMembers": "No members or managers", "addMember": "Add Member", "selectMember": "Select a member...", "selectRole": "Select role...", "relations": "Relations", "noRelations": "No relations", "addRelation": "Add Relation", "selectProject": "Select a project...", "selectRelationType": "Choose a relation type..."}}, "addOrEditModal": {"steps": {"createProject": "Create Project", "financialSettings": "Financial Settings", "categorization": "Categorization", "teamAndRelations": "Team & Relations"}}, "deleteModal": {"title": "Delete Selected?", "description": "You are about to permanently delete this project. Click 'Delete' to confirm the action"}, "archiveModal": {"title": "Archive {projectName}?", "description": "{affectedTasksCount} tasks will be affected by archiving {projectName}."}, "restoreModal": {"title": "Restore project", "description": "Are you sure you want to restore {projectName}?"}, "bulkActions": {"bulkArchiveModal": {"title": "Archive {projectsCount} projects", "description": "Are you sure you want to archive"}, "bulkRestoreModal": {"title": "Restore {projectsCount} projects", "description": "Are you sure you want to restore"}}}, "home": {"BREADCRUMB": "[\"Dashboard\"]", "SENT_EMAIL_VERIFICATION": "Code Verification has been sent to your email", "SENT_EMAIL_VERIFICATION_YOU_NEED_TO": "You need to ", "SENT_EMAIL_VERIFICATION_YOUR_EMAIL_ADDRESS": " your email address. The verification code should already be sent to your email, but feel free to click ", "SENT_EMAIL_VERIFICATION_RESEND": " to resend the code, if you did not receive it.", "INVITATIONS": "You've been invited to join", "CONFIRM_ACCEPT_INVITATION": "Are you sure you want to accept the invitation?", "CONFIRM_REJECT_INVITATION": "Are you sure you want to reject the invitation?", "OUTSTANDING_NOTIFICATIONS": {"SUBJECT": "You have", "USER_LABEL": "uncompleted tasks, please check in", "OUTSTANDING_VIEW": "Outstanding", "VIEW_BUTTON": "View"}}, "profile": {"BREADCRUMB": "[\"Member Tasks\"]", "MEMBER_NOT_FOUND_MSG_1": "The member you are looking is not exist in a team please try to search with a different query.", "MEMBER_NOT_FOUND_MSG_2": "If you are looking for a member in a different team, please select the team from the dropdown above.", "GO_TO_HOME": "Go to Home"}, "kanban": {"KANBAN_BOARD": "Kanban Board"}, "taskDetails": {"BREADCRUMB": "[\"Task Details\"]", "DESCRIPTION": "Descriptions", "ADD_DESCRIPTION": "Add Description here", "TYPE_OF_ISSUE": "Type of Issue", "CREATOR": "Creator", "ASSIGNEES": "Assignees", "START_DATE": "Start Date", "DUE_DATE": "Due Date", "DAYS_REMAINING": "Days Remaining", "VERSION": "Version", "EPIC": "Epic", "STATUS": "Status", "LABEL": "Label", "LABELS": "Labels", "SIZE": "Size", "PRIORITY": "Priority", "ESTIMATIONS": "Estimations", "PROGRESS": "Progress", "TOTAL_TIME": "Total Time", "TIME_TODAY": "Time Today", "TOTAL_GROUP_TIME": "Total Group Time", "TIME_REMAINING": "Time Remaining", "CREATED": "Created", "UPDATED": "Updated", "RESOLVED": "Resolved", "TASK_TITLE_CHARACTER_LIMIT_ERROR_TITLE": "We couldn't update Task Title.", "TASK_TITLE_CHARACTER_LIMIT_ERROR_DESCRIPTION": "Task Title can't exceed 255 characters.", "TASK_IS_ALREADY_EPIC": "Epic Task Type can not be changed.", "TASK_HAS_PARENT": "Task Type can not be changed as Task has already Parent.", "PROJECT": "Project"}, "taskStatus": {"DELETE_STATUS_CONFIRMATION": "You are about to Delete the Status {statusName} that is used by active tasks; please confirm action"}, "auth": {"SEND_CODE": "send code", "RESEND_CODE": "Resend Code", "RESEND_CODE_IN": "Resend Code in", "JOIN": "Join", "UNRECEIVED_CODE": "Didn't receive code ?", "JOIN_TEAM": "Join Team", "INPUT_INVITE_CODE": "Input invitation code.", "INPUT_INVITE_CODE_DESC": "Enter the invitation code we sent to your email.", "INVALID_INVITE_CODE_MESSAGE": "Invalid Code", "WELCOME_TEAMS": "Real-Time Clarity, Real-Time Reality™.", "COVER_TITLE": "Open Work and Project Management Platform", "COVER_DESCRIPTION": "All-In-One Work & Workforce Management, Time Management, Time Tracking, Activity Tracking, Productivity Tracking & Metrics, Projects / Tasks & Issues Management, Organizations & Teams, Integrations (GitHub, JIRA, ...) and More!", "LOGIN": "<PERSON><PERSON>", "SELECT_WORKSPACE": "Select Workspace", "ENTER_EMAIL": "<PERSON><PERSON>", "WORKSPACES_NOT_FOUND": "Workspaces Not Found", "INVALID_CODE_TRY_AGAIN": "Invalid code. Please try again."}, "authPasscode": {"HEADING_TITLE": "Join existing Team", "HEADING_DESCRIPTION": "Please enter your email and invitation code to join an existing team."}, "authLogin": {"HEADING_TITLE": "Log In to Ever Teams", "HEADING_DESCRIPTION": "Please enter your email below to begin the login process.", "WORKSPACE": "Workspace", "HEADING_WORKSPACE_LINE1": "The email associated with multiple workspaces,", "HEADING_WORKSPACE_LINE2": "Please select one to continue", "HAVE_PASSWORD": "Have Password?", "LOGIN_WITH_PASSWORD": "Login with the Password", "LOGIN_WITH_MAGIC_CODE": "Log in with a magic code", "ERROR_SIGNIN": "Error on signing"}, "authPassword": {"HEADING_DESCRIPTION": "Please enter your login information."}, "authTeam": {"HEADING_TITLE": "Create New Team", "HEADING_DESCRIPTION": "Please enter your team details to create a new team.", "LOADING_TEXT": "We are now creating your new workplace, hold on...", "REDIRECT_TO_WORSPACE_LOADING": "Redirecting to your workspace...", "VERIFY_EMAIL_LOADING_TEXT": "We are verifying your email, hold on...", "INPUT_TEAM_NAME": "Input your team name", "JOIN_EXISTING_TEAM": "Joining existing team?", "CREATE_FIRST_TEAM": "Create your first team", "CREATE_TEAM": "Create team"}, "settings": {"BREADCRUMB": "[\"<PERSON><PERSON>\", \"<PERSON>ting<PERSON>\"]", "DANDER_ZONE": "Danger Zone", "HEADING_DESCRIPTION": "Setting dan manage your dashboard here", "ARE_YOU_SURE_TO_DELETE_USER": "Are you sure you want to delete this user?"}, "settingsPersonal": {"HEADING_TITLE": "General Settings", "emailNotValid": "Please provide a valid Email", "phoneNotValid": "Please provide a valid Phone Number", "WORK_SCHEDULE": "Work Schedule", "SUBSCRIPTION": "Subscription", "ABOUT_TO_CHANGE_EMAIL": "You are about to change Email", "ABOUT_TO_DELETE_ACCOUNT": "You are about to Delete your account ?", "TIMEZONE_SEARCH_PLACEHOLDER": "Your Timezone", "ABOUT_TO_REMOVE_FROM_ALL_TEAMS": "You're about to be removed from all teams unless you're the only manager ?", "DATA_SYNCHRONIZATION": "Data Synchronization", "ABOUT_TO_DELETE_ALL_ACCOUNT_DATA": "You are about to Delete your account and all your data ?", "ABOUT_TO_REMOVE_ACCOUNT": "You are about to remove your account"}, "settingsTeam": {"HEADING_TITLE": "General Settings", "MEMBER_HEADING_TITLE": "Members", "PRIORITIES_HEADING": "Priorities", "NOTIFICATION_HEADING": "Notifications", "SIZES_HEADING": "Sizes", "TEAM_NAME": "Team Name", "TEAM_TYPE": "Team Type", "TIME_TRACKING": "Time Tracking", "ENFORCE_PLAN": "Enforce plan", "ADD_NEW_MEMBER": "Add new member", "MANAGE_ASSIGNEES": "Manage Assignees", "SEARCH_MEMBER": "Search Member", "TASK_STATUSES": "Task Statuses", "SORT_TASK_STATUSES": "Sort Task Statuses", "TASK_PRIORITIES": "Task Priorities", "TASK_SIZES": "Task Sizes", "TASK_LABELS": "Task Labels", "ISSUE_TYPES": "Issue Types", "DEFAULT_ISSUE_TYPE": "Default Issue Type", "NO_DEFAULT_ISSUE_TYPE": "No Default Issue Type", "RELATED_TYPE": "Related Type", "INVITATION_HEADING_TITLE": "Invitations", "CREATE_NEW_STATUSES": "Create new Statuses", "CREATE_NEW_STATUS": "Create new Status", "CREATE_NEW_VERSION": "Create new Version", "CREATE_NEW_PRIORITIES": "Create new Priorities", "CREATE_NEW_PRIORITY": "Create new Priority", "CREATE_NEW_SIZES": "Create new Sizes", "CREATE_NEW_SIZE": "Create new Size", "CREATE_NEW_LABELS": "Create new Labels", "CREATE_NEW_LABEL": "Create new Label", "CREATE_NEW_ISSUE_TYPES": "Create new Issues", "HIDE_PERSONAL_MEMBERS_INFOTMATION": "Hide Personal Members Information", "POSITION_CUSTOM": "Positions custom", "INVITATION_EXPIRATION": "Invitation Expiration", "NOTIFY_IF": "Notify if", "TEAM_REQUEST": "Team Request", "WORK_SCHEDULE": "Work schedule", "MEMBER_AND_ROLES": "Members & Roles", "INTEGRATIONS": "Integrations", "DANDER_ZONES": "Danger Zone", "GITHUB": "GitHub", "GITHUB_INTEGRATION_DESCRIPTION": "Connect with GitHub and start Syncing GitHub issues with your Team", "SELECT_REPOSITORY": "Select Repository", "INSTALL": "Install", "LIST_OF_STATUSES": "List of Statuses", "LIST_OF_PRIORITIES": "List of Priorities", "LIST_OF_SIZES": "List of Sizes", "LIST_OF_LABELS": "List of Labels", "LIST_OF_ISSUES_TYPE": "List of Issues", "GENERAL": "General", "SOUND": "Sound", "EMAIL": "Email", "USERS": "Users", "TASKS": "Tasks", "SYSTEM": "System", "SECURITY": "Security", "INAPP": "In App", "NOTIFICATION_HEADING_TITLE": "Notification Settings", "ISSUE_HEADING_TITLE": "Issue Settings", "TASK_PRIVACY": "Task Privacy", "MULTIPLE_ASSIGNEES": "Multiple Assignees", "MANUAL_TIME": "Manual Time", "GROUP_ESTIMATION": "Group Estimation", "ESTIMATION_IN_HOURS": "Estimation in Hours", "ESTIMATION_IN_STORY_POINTS": "Estimation in Story Points", "PROOF_OF_COMPLETION": "Proof of Completion", "LINKED_ISSUES": "Linked Issues", "COMMENTS": "Comments", "HISTORY": "History", "ACCEPTANCE_CRITERIA": "Acceptance criteria", "DRAFT_ISSUES": "Draft Issues", "AUTO_CLOSE_ISSUE": "Auto-close issue", "AUTO_ARCHIVE_ISSUE": "Auto-archive issue", "AUTO_STATUS": "Auto-Status", "VERSIONS": "Versions", "LIST_OF_VERSONS": "List of Versions", "ISSUETYPE": "Issue Types", "LIST_OF_ISSUES": "List of Issues", "RELATED_ISSUE_TYPE": "Related Issue Types", "LIST_OF_RELATED_TYPE": "List of Related Types", "INITIAL": "Initial", "COPY_NUMBER": "Copy Number", "IN_PROGRESS": "In Progress", "FINAL": "Final", "DISPOSE_TEAM": "You are about to Dispose the team ?", "QUIT_TEAM": "You are about to Quit team ?", "TEAM_COLOR": "Team Color", "TEAM_SIZE": "Team Size", "EMOJI": "<PERSON><PERSON><PERSON>", "ISSUES_HEADING_TITLE": "Issues Settings", "TRACK_TIME": "Track Time", "ESTIMATE_ISSUE": "Estimate issue", "EPICS_CREATE_CLOSE": "Epics Create/Close", "ISSUE_CREATE_CLOSE": "Issue Create/Close", "ISSUE_ASSIGN_UNASSIGN": "Issue Assign/Unassign", "INVITE_MEMBERS": "Invite members", "REMOVE_MEMBERS": "Remove members", "HANDLE_REQUESTS": "Handle requests", "ROLES_POSITIONS_CHANGE": "Roles/Positions change", "VIEW_DETAILS": "View Details", "NO_INVITATIONS": "There are no Invitations at the moment!", "NO_MEMBERS": "There are no Members at the moment!", "GO_TO_PERSONAL_SETTINGS": "Go to Personal settings", "SHARE_MEMBERS_PROFILE_VIEWS": "Share members profile views"}, "invite": {"HEADING_TITLE": "Invite member to your team", "HEADING_DESCRIPTION": "Send an invitation to a team member by email", "TEAM_MEMBER_EMAIL": "Team Member's Email", "TEAM_MEMBER_FULLNAME": "Team Member's Full Name", "SEND_INVITE": "Send Invitation", "ERROR_WHILE_ACCEPTING_INVITATION": "Error while accepting invitation", "ERROR_WHILE_REJECTING_INVITATION": "Error while rejecting invitation", "INVITE_LABEL_SEND": "Send Invite", "invitationTable": {"NAME_AND_EMAIL": "Name & Email", "POSITION": "Position", "DATE_AND_TIME_REQUEST": "Date & Time Request", "CV_OR_ATTACHMENT": "CV / Attachment"}, "acceptInvite": {"ACCEPT_INVITATION_TO_TEAM": "Accept invitation to {team} team", "USER_DETAILS": "User details", "COMPLETE_REGISTRATION": "Complete your registration {userEmail}", "messages": {"INVALID_INVITATION": {"TITLE": "This invitation is no longer valid", "DESCRIPTION": "Please contact the workspace admin to request a new one. Either you entered an incorrect URL or the invitation has expired"}, "WORKSPACE_NOT_EXIST": {"TITLE": "This workspace no longer exists", "DESCRIPTION": "Please contact the workspace admin to request a new one"}}, "buttons": {"RETURN_TO_DASHBOARD": "Return to dashboard", "CREATE_NEW_WORKSPACE": "Create a new workspace"}}}, "permissions": {"SELECT_ROLES": "Select Roles"}, "unauthorized": {"TITLE": "Unauthorized", "HEADING_TITLE": "You are not authorized to access this page !", "HEADING_DESCRIPTION": "We apologize for the inconvenience, but you are not authorized to access to this page. If you believe this is an error. Please login.", "TRY_AGAIN": "Try again"}, "page404": {"HEADING_TITLE": "Page not found !", "HEADING_DESCRIPTION": "We looked, but can't find it .... ", "LINK_LABEL": "Home"}, "offline": {"HEADING_TITLE": "Network Disconnected!", "HEADING_SUB_DESCRIPTION": "Oops, we lost you. Don't worry, your time is still tracking.", "HEADING_DESCRIPTION": "You will be able to continue working in the web app once your connection is restored.", "STATUS_NETWORK": "Offline!"}, "error": {"TITLE": "Error !", "HEADING_TITLE": "Something went wrong !", "HEADING_DESCRIPTION": "If the problem persists, send a distress signal to our support team."}, "maintenance": {"HEADING_TITLE": "We are Under Maintenance", "HEADING_DESCRIPTION": "We are updating our website to serve you better. Please check back later."}, "timesheet": {"TIMESHEET_TITLE": "Timesheet", "TIMESHEET_VIEW_DETAILS": "View Details", "TIMESHEET_ACTION_APPROVE_SELECTED": "Approve selected", "TIMESHEET_ACTION_REJECT_SELECTED": "Reject selected", "TIMESHEET_ACTION_DELETE_SELECTED": "Delete selected", "HEADING_DESCRIPTION": "This is your personal timesheet dashboard, showing you what needs your attention now.", "GREETINGS": {"GOOD_MORNING": "Good morning", "GOOD_AFTERNOON": "Good afternoon", "GOOD_EVENING": "Good evening"}, "VIEWS": {"LIST": "List View", "CALENDAR": "Calendar View"}, "BILLABLE": {"YES": "Yes", "NO": "No", "BILLABLE": "BILLABLE"}, "LOADING": "Loading timesheet data...", "NO_ENTRIES_FOUND": "No timesheet entries found", "YOU_ARE_ABOUT_TO_REJECT_ENTRY": "You are about to reject the selected entry, would you like to proceed?", "ALL_TASKS": "All Tasks", "PENDING": "Pending", "APPROVED": "Approved", "IN_REVIEW": "In review", "DRAFT": "Draft", "REJECTED": "Rejected"}}, "timer": {"START_TIMER": "Please, select or create a new task to start tracking the time or create a plan", "TEAM_SWITCH": {"STOPPED_TIMER_TOAST_TITLE": "Timer Stopped", "STOPPED_TIMER_TOAST_DESCRIPTION": "Timer Stopped due to Team Switch"}, "ESTIMATION": {"ESTIMATE_LABEL": "Estimate"}, "DAY_LIST": [{"title": "7 days"}, {"title": "14 days"}], "TIME_ACTIVITY": "Activity", "TOTAL_HOURS": "Total Hours", "NO_SCREENSHOOT": "No Screenshots", "PERCENT_OF_MINUTES": " % of 10 Minutes", "APPS": "Apps", "VISITED_DATES": "Visited Dates", "PERCENT_USED": "Percent Used", "TIME_SPENT_IN_HOURS": "Time spent (Hours)", "THERE_IS_NO_APPS_VISITED": "There are no Apps Visited.", "OTHER_DETAILS": "Other details", "KEYBOARD": "keyboard", "MOUSE": "Mouse", "TIMES": "Times", "ACTIVE": "Active", "INACTIVE": "Inactive", "ARCHIVED": "Archived", "NOT_ARCHIVED": "Not archived", "todayPlanSettings": {"TITLE": "TODAY'S PLAN", "WORK_TIME_PLANNED": "Add planned working hours", "WORK_TIME_PLANNED_PLACEHOLDER": "Working time planned for today", "TASKS_WITH_NO_ESTIMATIONS": "Tasks with no time estimations", "START_WORKING_BUTTON": "Start working", "TIMER_RUNNING": "The timer is already running", "WARNING_PLAN_ESTIMATION": "Please correct planned work hours or  re-estimate task(s)"}, "VISITED_SITES": "Visited sites", "NO_VISITED_SITE_MESSAGE": "There are no visited sites"}, "task": {"TITLE": "Task", "TITLE_PLURAL": "Tasks", "ASSIGN_NEW_TASK": "Assign new task", "ASSIGNED_BY": "Assigned By", "NO_ONE_FOR_TASK": "No One Assigned to this Task", "WORKED_TODAY_ON_TASK_TOOLTIP": "worked today on this task for", "WORKED_TOTAL_ON_TASK_TOOLTIP": "worked on this task for a total of", "WORKED_TODAY_ON_ALL_TOOLTIP": "worked today on all", "TASKS_FOR_TOOLTIP": "tasks for", "taskLabel": {"WORK_LABEL": "Worked", "TASK_ASSIGNED": "Assigned", "TASK_UNASSIGNED": "Unassigned", "BUTTON_LABEL": "Assign Task"}, "tabFilter": {"WORKED_DESCRIPTION": "This tab shows all tasks you started working on", "ASSIGNED_DESCRIPTION": "This tab shows all tasks that are assigned to you", "UNASSIGNED_DESCRIPTION": "This tab shows all tasks that are not assigned to you", "NO_TASK_USER_ASSIGNED": "No one assigned  to this Task", "DAILYPLAN_DESCRIPTION": "This tab shows all your planned tasks", "TODAY_TASKS": "Today Tasks", "FUTURE_TASKS": "Future Tasks", "PAST_TASKS": "Past Tasks", "ALL_TASKS": "All Tasks", "OUTSTANDING": "Outstanding"}, "taskTableHead": {"TASK_NAME": "Name", "TASK_STATUS": "Status", "TASK_WORK": {"TITLE": "Worked on", "DESCRIPTION": "Task", "LABEL": "Worked on task"}, "TASK_TIME": "Estimate", "TOTAL_WORK": {"TITLE": "Total Worked", "DESCRIPTION": "Today", "LABEL": "Total worked Today"}, "TOTAL_WORKED_TODAY_HEADER_TOOLTIP": "How many hours an employee worked for total and today on task", "WORKED_ON_TASK_HEADER_TOOLTIP": "How many hours did employees work today on all tasks for the selected Team"}, "CONFIRM_CLOSE_TASK": "Please confirm if you want to close the task", "toastMessages": {"TASK_UNASSIGNED": "Task unassigned", "TASK_ASSIGNED": "Task assigned successfully", "TASK_ADDED_TO_FAVORITES": "Task added to favorites", "TASK_REMOVED_FROM_FAVORITES": "Task removed from favorites", "TASK_PLANNED_FOR_TODAY": "Task planned for today", "TASK_PLANNED_FOR_TOMORROW": "Task planned for tomorrow", "TASK_PLANNED_FOR_DATE": "Task planned for selected date", "TASK_PLAN_FAILED": "Failed to plan task. Please try again.", "TASK_ASSIGNMENT_FAILED": "Failed to assign/unassign task. Please try again.", "TASK_FAVORITE_FAILED": "Failed to update favorites. Please try again.", "FAVORITE_ITEM_CREATED_SUCCESSFULLY": "Favorite item created successfully", "FAVORITE_ITEM_DELETED_SUCCESSFULLY": "Favorite item deleted successfully"}}, "dailyPlan": {"PLAN_FOR_TODAY": "Plan for today", "TASK_TIME": "task time", "TOTAL_TODAY": "Total Today", "PLAN_FOR_TOMORROW": "Plan for tomorrow", "PLAN_FOR_SOME_DAY": "Plan for selected day", "ADD_TASK_TO_PLAN": "Add this task to a plan", "REMOVE_FROM_THIS_PLAN": "Remove from this plan", "CREATE_A_PLAN_FOR_TODAY": "Create a Plan for Today", "TODAY_PLAN_SUB_TITLE": " There is no plan for Today", "NO_TASK_PLANNED": "No task planned", "NO_TASK_PLANNED_TODAY": "No task planned Today", "PLANNED_TIME": "Planned time", "ESTIMATED_TIME": "Estimated time", "TOTAL_TIME_WORKED": "Total time worked", "COMPLETED_TASKS": "Completed tasks", "READY": "Ready", "LEFT": "Left", "COMPLETION": "Completion", "PLANNED_TASKS": "Planned task", "ESTIMATED": "Estimated", "TOTAL_ESTIMATED": "Total Estimated", "TOTAL_TASK": "Total tasks", "DAILY_PLAN_DESCRIPTION": "'Daily Plan' helps organize the work process to achieve the best results", "SUGGESTS_TO_ADD_TASK_TO_TODAY_PLAN": "This task was not planned for Today's plan. Would you like to add to the plan?", "planned_tasks_popup": {"warning": {"PLANNED_TIME": "Please, add planned time", "TASKS_ESTIMATION": "Please, estimate all tasks", "OPTIMIZE_PLAN": "Total estimated time exceeds your Planned time, please optimize your plan", "PLAN_MORE_TASKS": "Please add more tasks to the plan", "PLEASE_ADD_TASKS": "Please, add tasks"}}, "chang_active_task_popup": {"TITLE": "Change Active Task", "MESSAGE": "You are about to start working on Today's plan {defaultPlannedTask}, but still have active {activeTask}"}}, "form": {"NAME_PLACEHOLDER": "Enter your name", "FIRST_NAME_PLACEHOLDER": "First Name", "LAST_NAME_PLACEHOLDER": "Last Name", "EMAIL_PLACEHOLDER": "Enter your email address", "PASSWORD_PLACEHOLDER": "Enter your password", "CONFIRM_PASSWORD_PLACEHOLDER": "Confirm your password", "PHONE_PLACEHOLDER": "Phone Number", "ISSUE_NAME_PLACEHOLDER": "Issue name", "TEAM_NAME_PLACEHOLDER": "Please Enter your team name", "TASK_INPUT_PLACEHOLDER": "What you working on?", "TEAM_MEMBER_NAME_PLACEHOLDER": "Team member name", "TEAM_MEMBER_EMAIL_PLACEHOLDER": "Team member email address", "TEAM_MEMBER_ROLE": "Team member role", "COMPLETION_DESCRIPTION": "Write the description", "UPLOAD_FILES": "Upload Files", "ATTACHMENT_FILE": "Attachment files", "ADD_COMMENT": "Add Comment here", "SET_THEME": "Set Theme", "CHANGE_AVATAR": "Change Avatar", "SELECT_TEAM_SIZE": "Select Team Size", "INVALID_ALLOWED_CHARACTER": "Invalid value for allowed characters. Use alpha, numeric, or alphanumeric", "AGREE_TO": "Agree to"}, "layout": {"footer": {"RIGHTS_RESERVED": "All rights reserved.", "COPY_RIGHT1": "© {date}-Present,", "COPY_RIGHT2": "Ever Teams", "COPY_RIGHT4": "Ever Co.", "COMPANY_NAME": "Ever Co. LTD.", "TERMS": "Terms of Service", "TERMS_AND_CONDITIONS": "Terms and Conditions", "PRIVACY_POLICY": "Privacy Policy", "COOKIES": "<PERSON><PERSON>", "BY": "By"}}, "errors": {"NETWORK_ISSUE": "Network issue, please try again later", "ERROR_WHILE_VERIFY_CODE": "Error while verifying code.", "LENGTH_NUMBER_ERROR": "Length should be a number and greater than 0", "INVALID_ALLOWED_CHARACTER": "Invalid value for allowed characters. Use alpha, numeric, or alphanumeric", "VALID_EMAIL": "Please enter valid email", "SELECT_ROLE": "Please select a role", "PASSWORDS_DO_NOT_MATCH": "Passwords do not match"}, "placeholders": {"ENTER_TO_VALIDATE": "Press Enter to validate"}, "team": {"BACK_LABEL": "Back to Team", "PUBLIC_TEAM": "Public Team", "PRIVATE_TEAM": "Private Team", "creation": {"NEW": "Create new team"}}, "links": {"common": {"TASKS": "Tasks", "TEAM": "Team", "SETTINGS": "Settings"}}, "manualTime": {"ADDED_HOURS": "Added Hours", "DATE": "Date", "PICK_A_DATE": "Pick a Date", "START_TIME": "Start Time", "END_TIME": "End Time", "TEAM": "Team", "EMPLOYEE": "Employee", "TASK": "Task", "DESCRIPTION": "Description", "OPTIONAL": "Optional", "REASON": "Reason ", "reasons": {"DEFAULT": "Only manual time", "LOST_ELECTRICITY": "Lost electricity", "LOST_INTERNET": "Lost internet", "FORGOT_TO_START_TIMER": "Forgot to start timer", "ERROR": "Error", "UNPLANNED_WORK": "Unplanned work", "TESTED_TIMER": "Tested timer"}}, "timeSlot": {"DELETE_MESSAGE": "Are you sure you want to delete this time slot?"}}